import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../model/media_item.dart';
import '../services/unified_audio_player_service.dart';

/// قائمة الخيارات المتقدمة للملفات الصوتية
/// تحتوي على خيارات حذف، إخفاء، تعديل وإعدادات الصوت
class AdvancedOptionsMenu extends StatelessWidget {
  final MediaItem mediaItem;
  final VoidCallback? onDeleted;
  final VoidCallback? onHidden;
  final VoidCallback? onEdited;

  const AdvancedOptionsMenu({
    super.key,
    required this.mediaItem,
    this.onDeleted,
    this.onHidden,
    this.onEdited,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مؤشر السحب
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // معلومات الملف
          _buildFileInfo(),
          
          const SizedBox(height: 24),
          
          // خيارات الإجراءات
          _buildActionOptions(),
          
          const SizedBox(height: 20),
          
          // زر الإغلاق
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الملف
  Widget _buildFileInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Get.theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          // صورة الملف
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.primary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.music_note,
              color: Get.theme.colorScheme.primary,
              size: 30,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // معلومات الملف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  mediaItem.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Get.theme.colorScheme.onSurface,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  mediaItem.artist ?? 'مجهول',
                  style: TextStyle(
                    fontSize: 14,
                    color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatFileSize(mediaItem.size ?? 0),
                  style: TextStyle(
                    fontSize: 12,
                    color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
          
          // زر التعديل السريع
          IconButton(
            onPressed: () => _showEditDialog(),
            icon: Icon(
              Icons.edit,
              color: Get.theme.colorScheme.primary,
            ),
            tooltip: 'تعديل سريع',
          ),
        ],
      ),
    );
  }

  /// بناء خيارات الإجراءات
  Widget _buildActionOptions() {
    return Column(
      children: [
        // إضافة للمفضلة
        _buildOptionTile(
          icon: Icons.favorite_border,
          title: 'إضافة للمفضلة',
          subtitle: 'حفظ في قائمة المفضلة',
          color: Colors.pink,
          onTap: _addToFavorites,
        ),
        
        const SizedBox(height: 8),
        
        // إضافة لقائمة تشغيل
        _buildOptionTile(
          icon: Icons.playlist_add,
          title: 'إضافة لقائمة تشغيل',
          subtitle: 'اختيار قائمة تشغيل',
          color: Colors.blue,
          onTap: _addToPlaylist,
        ),
        
        const SizedBox(height: 8),
        
        // مشاركة الملف
        _buildOptionTile(
          icon: Icons.share,
          title: 'مشاركة الملف',
          subtitle: 'مشاركة مع التطبيقات الأخرى',
          color: Colors.green,
          onTap: _shareFile,
        ),
        
        const SizedBox(height: 8),
        
        // إعدادات الصوت
        _buildOptionTile(
          icon: Icons.equalizer,
          title: 'إعدادات الصوت',
          subtitle: 'معادل الصوت والتأثيرات',
          color: Colors.purple,
          onTap: _showAudioSettings,
        ),
        
        const SizedBox(height: 8),
        
        // تعديل معلومات الملف
        _buildOptionTile(
          icon: Icons.edit_note,
          title: 'تعديل معلومات الملف',
          subtitle: 'تغيير الاسم والفنان والألبوم',
          color: Colors.orange,
          onTap: _showEditDialog,
        ),
        
        const SizedBox(height: 8),
        
        // إخفاء الملف
        _buildOptionTile(
          icon: Icons.visibility_off,
          title: 'إخفاء الملف',
          subtitle: 'نقل إلى الملفات المخفية',
          color: Colors.grey,
          onTap: _hideFile,
        ),
        
        const SizedBox(height: 8),
        
        // حذف الملف
        _buildOptionTile(
          icon: Icons.delete,
          title: 'حذف الملف',
          subtitle: 'حذف نهائي من الجهاز',
          color: Colors.red,
          onTap: _deleteFile,
        ),
      ],
    );
  }

  /// بناء عنصر خيار
  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Get.theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 16),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Get.theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              
              Icon(
                Icons.arrow_forward_ios,
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.5),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// إضافة للمفضلة
  void _addToFavorites() {
    Get.back();
    // TODO: تنفيذ إضافة للمفضلة
    Get.snackbar(
      'تم',
      'تم إضافة "${mediaItem.title}" للمفضلة',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// إضافة لقائمة تشغيل
  void _addToPlaylist() {
    Get.back();
    // TODO: عرض قوائم التشغيل المتاحة
    Get.snackbar(
      'قريباً',
      'ميزة إضافة لقائمة التشغيل ستكون متاحة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// مشاركة الملف
  void _shareFile() {
    Get.back();
    // TODO: تنفيذ مشاركة الملف
    Get.snackbar(
      'قريباً',
      'ميزة مشاركة الملف ستكون متاحة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// عرض إعدادات الصوت
  void _showAudioSettings() {
    Get.back();
    Get.bottomSheet(
      _AudioSettingsWidget(mediaItem: mediaItem),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }

  /// عرض حوار التعديل
  void _showEditDialog() {
    Get.back();
    Get.dialog(
      _EditFileDialog(
        mediaItem: mediaItem,
        onSaved: onEdited,
      ),
    );
  }

  /// إخفاء الملف
  void _hideFile() {
    Get.back();
    Get.dialog(
      AlertDialog(
        title: const Text('إخفاء الملف'),
        content: Text('هل تريد إخفاء "${mediaItem.title}"؟\nيمكنك الوصول إليه من الإعدادات > الملفات المخفية'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // TODO: تنفيذ إخفاء الملف
              onHidden?.call();
              Get.snackbar(
                'تم',
                'تم إخفاء الملف بنجاح',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            child: const Text('إخفاء'),
          ),
        ],
      ),
    );
  }

  /// حذف الملف
  void _deleteFile() {
    Get.back();
    Get.dialog(
      AlertDialog(
        title: const Text('حذف الملف'),
        content: Text('هل تريد حذف "${mediaItem.title}" نهائياً؟\nلا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _performDelete();
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// تنفيذ الحذف
  void _performDelete() async {
    try {
      final file = File(mediaItem.path);
      if (await file.exists()) {
        await file.delete();
        onDeleted?.call();
        Get.snackbar(
          'تم',
          'تم حذف الملف بنجاح',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'الملف غير موجود',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في حذف الملف: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// عرض قائمة الخيارات
  static void show(MediaItem mediaItem, {
    VoidCallback? onDeleted,
    VoidCallback? onHidden,
    VoidCallback? onEdited,
  }) {
    Get.bottomSheet(
      AdvancedOptionsMenu(
        mediaItem: mediaItem,
        onDeleted: onDeleted,
        onHidden: onHidden,
        onEdited: onEdited,
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}

/// ويدجت إعدادات الصوت
class _AudioSettingsWidget extends StatelessWidget {
  final MediaItem mediaItem;

  const _AudioSettingsWidget({required this.mediaItem});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'إعدادات الصوت',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 20),
          const Text('ميزة إعدادات الصوت ستكون متاحة قريباً'),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Get.back(),
              child: const Text('إغلاق'),
            ),
          ),
        ],
      ),
    );
  }
}

/// حوار تعديل الملف
class _EditFileDialog extends StatefulWidget {
  final MediaItem mediaItem;
  final VoidCallback? onSaved;

  const _EditFileDialog({required this.mediaItem, this.onSaved});

  @override
  State<_EditFileDialog> createState() => _EditFileDialogState();
}

class _EditFileDialogState extends State<_EditFileDialog> {
  late TextEditingController _titleController;
  late TextEditingController _artistController;
  late TextEditingController _albumController;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.mediaItem.title);
    _artistController = TextEditingController(text: widget.mediaItem.artist ?? '');
    _albumController = TextEditingController(text: widget.mediaItem.album ?? '');
  }

  @override
  void dispose() {
    _titleController.dispose();
    _artistController.dispose();
    _albumController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تعديل معلومات الملف'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'اسم الأغنية',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _artistController,
            decoration: const InputDecoration(
              labelText: 'الفنان',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _albumController,
            decoration: const InputDecoration(
              labelText: 'الألبوم',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveChanges,
          child: const Text('حفظ'),
        ),
      ],
    );
  }

  void _saveChanges() {
    // TODO: تنفيذ حفظ التغييرات
    Get.back();
    widget.onSaved?.call();
    Get.snackbar(
      'تم',
      'تم حفظ التغييرات بنجاح',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
