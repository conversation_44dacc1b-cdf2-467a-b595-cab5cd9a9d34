import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../model/media_item.dart';

/// ويدجت الترتيب والفلترة
/// يوفر خيارات ترتيب الملفات حسب معايير مختلفة
class SortFilterWidget extends StatelessWidget {
  final List<MediaItem> items;
  final Function(List<MediaItem>) onSorted;
  final String currentSortType;
  final bool isAscending;

  const SortFilterWidget({
    super.key,
    required this.items,
    required this.onSorted,
    this.currentSortType = 'name',
    this.isAscending = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مؤشر السحب
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // عنوان الترتيب
          Row(
            children: [
              Icon(
                Icons.sort,
                color: Get.theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'ترتيب الملفات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              Text(
                '${items.length} ملف',
                style: TextStyle(
                  color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // خيارات الترتيب
          _buildSortOptions(),
          
          const SizedBox(height: 20),
          
          // اتجاه الترتيب
          _buildSortDirection(),
          
          const SizedBox(height: 20),
          
          // أزرار الإجراءات
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// بناء خيارات الترتيب
  Widget _buildSortOptions() {
    final sortOptions = [
      {'key': 'name', 'title': 'الاسم', 'icon': Icons.sort_by_alpha},
      {'key': 'artist', 'title': 'الفنان', 'icon': Icons.person},
      {'key': 'album', 'title': 'الألبوم', 'icon': Icons.album},
      {'key': 'date', 'title': 'تاريخ الإضافة', 'icon': Icons.date_range},
      {'key': 'duration', 'title': 'المدة', 'icon': Icons.timer},
      {'key': 'size', 'title': 'الحجم', 'icon': Icons.storage},
    ];

    return Column(
      children: sortOptions.map((option) {
        final isSelected = currentSortType == option['key'];
        
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: () => _sortBy(option['key'] as String),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? Get.theme.colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected 
                        ? Get.theme.colorScheme.primary.withValues(alpha: 0.3)
                        : Get.theme.colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      option['icon'] as IconData,
                      color: isSelected 
                          ? Get.theme.colorScheme.primary
                          : Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        option['title'] as String,
                        style: TextStyle(
                          color: isSelected 
                              ? Get.theme.colorScheme.primary
                              : Get.theme.colorScheme.onSurface,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        Icons.check_circle,
                        color: Get.theme.colorScheme.primary,
                        size: 20,
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// بناء اتجاه الترتيب
  Widget _buildSortDirection() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildDirectionButton(
              true,
              'تصاعدي',
              Icons.arrow_upward,
              isAscending,
            ),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: _buildDirectionButton(
              false,
              'تنازلي',
              Icons.arrow_downward,
              !isAscending,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر اتجاه الترتيب
  Widget _buildDirectionButton(bool ascending, String label, IconData icon, bool isSelected) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => _changeSortDirection(ascending),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected 
                ? Get.theme.colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected 
                    ? Colors.white
                    : Get.theme.colorScheme.primary,
                size: 18,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected 
                      ? Colors.white
                      : Get.theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _resetSort,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة تعيين'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              side: BorderSide(color: Get.theme.colorScheme.outline),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _shuffleItems,
            icon: const Icon(Icons.shuffle),
            label: const Text('خلط عشوائي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Get.theme.colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// ترتيب حسب معيار معين
  void _sortBy(String sortType) {
    List<MediaItem> sortedItems = List.from(items);
    
    switch (sortType) {
      case 'name':
        sortedItems.sort((a, b) => isAscending 
            ? a.title.toLowerCase().compareTo(b.title.toLowerCase())
            : b.title.toLowerCase().compareTo(a.title.toLowerCase()));
        break;
        
      case 'artist':
        sortedItems.sort((a, b) {
          final artistA = (a.artist ?? 'مجهول').toLowerCase();
          final artistB = (b.artist ?? 'مجهول').toLowerCase();
          return isAscending 
              ? artistA.compareTo(artistB)
              : artistB.compareTo(artistA);
        });
        break;
        
      case 'album':
        sortedItems.sort((a, b) {
          final albumA = (a.album ?? 'مجهول').toLowerCase();
          final albumB = (b.album ?? 'مجهول').toLowerCase();
          return isAscending 
              ? albumA.compareTo(albumB)
              : albumB.compareTo(albumA);
        });
        break;
        
      case 'date':
        sortedItems.sort((a, b) {
          final dateA = a.dateAdded ?? DateTime.fromMillisecondsSinceEpoch(0);
          final dateB = b.dateAdded ?? DateTime.fromMillisecondsSinceEpoch(0);
          return isAscending 
              ? dateA.compareTo(dateB)
              : dateB.compareTo(dateA);
        });
        break;
        
      case 'duration':
        sortedItems.sort((a, b) {
          final durationA = a.duration ?? Duration.zero;
          final durationB = b.duration ?? Duration.zero;
          return isAscending 
              ? durationA.compareTo(durationB)
              : durationB.compareTo(durationA);
        });
        break;
        
      case 'size':
        sortedItems.sort((a, b) {
          final sizeA = a.size ?? 0;
          final sizeB = b.size ?? 0;
          return isAscending 
              ? sizeA.compareTo(sizeB)
              : sizeB.compareTo(sizeA);
        });
        break;
    }
    
    onSorted(sortedItems);
    Get.back();
  }

  /// تغيير اتجاه الترتيب
  void _changeSortDirection(bool ascending) {
    if (ascending != isAscending) {
      _sortBy(currentSortType);
    }
  }

  /// إعادة تعيين الترتيب
  void _resetSort() {
    _sortBy('name');
  }

  /// خلط الملفات عشوائياً
  void _shuffleItems() {
    List<MediaItem> shuffledItems = List.from(items);
    shuffledItems.shuffle();
    onSorted(shuffledItems);
    Get.back();
  }

  /// عرض ويدجت الترتيب
  static void show({
    required List<MediaItem> items,
    required Function(List<MediaItem>) onSorted,
    String currentSortType = 'name',
    bool isAscending = true,
  }) {
    Get.bottomSheet(
      SortFilterWidget(
        items: items,
        onSorted: onSorted,
        currentSortType: currentSortType,
        isAscending: isAscending,
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}
