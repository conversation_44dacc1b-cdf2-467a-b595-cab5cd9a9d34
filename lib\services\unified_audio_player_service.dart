import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import '../model/media_item.dart';
import '../ controllers/theme_controller.dart';

/// خدمة المشغل الصوتي الموحد
/// تدير جميع عمليات التشغيل والتحكم في المشغل الكامل والمصغر
class UnifiedAudioPlayerService extends GetxController {
  static UnifiedAudioPlayerService get instance => Get.find<UnifiedAudioPlayerService>();
  
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ThemeController _themeController = Get.find<ThemeController>();

  // حالة المشغل
  var isPlaying = false.obs;
  var isLoading = false.obs;
  var currentPosition = Duration.zero.obs;
  var totalDuration = Duration.zero.obs;
  var playbackSpeed = 1.0.obs;

  // حالة العرض
  var isPlayerVisible = false.obs;
  var isMinimized = true.obs;
  var isFullScreen = false.obs;

  // قائمة التشغيل الحالية
  var currentPlaylist = <MediaItem>[].obs;
  var currentIndex = 0.obs;
  var currentMediaItem = Rxn<MediaItem>();

  // إعدادات التشغيل
  var repeatMode = RepeatMode.none.obs; // none, one, all
  var isShuffleEnabled = false.obs;
  var shuffledIndices = <int>[].obs;

  // إعدادات الترتيب والفلترة
  var sortOrder = SortOrder.name.obs;
  var isAscending = true.obs;
  var currentPlaylistName = 'جميع الأغاني'.obs;

  @override
  void onInit() {
    super.onInit();
    _initializePlayer();
  }

  @override
  void onClose() {
    _audioPlayer.dispose();
    super.onClose();
  }

  /// تهيئة المشغل والاستماع للأحداث
  void _initializePlayer() {
    // الاستماع لحالة التشغيل
    _audioPlayer.playingStream.listen((playing) {
      isPlaying.value = playing;
    });

    // الاستماع لموقع التشغيل
    _audioPlayer.positionStream.listen((position) {
      currentPosition.value = position;
    });

    // الاستماع لمدة الملف
    _audioPlayer.durationStream.listen((duration) {
      if (duration != null) {
        totalDuration.value = duration;
      }
    });

    // الاستماع لانتهاء التشغيل
    _audioPlayer.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        _onTrackCompleted();
      }
      isLoading.value = state.processingState == ProcessingState.loading;
    });
  }

  /// تشغيل ملف صوتي جديد
  Future<void> playMediaItem(MediaItem item, {List<MediaItem>? playlist, String? playlistName}) async {
    try {
      isLoading.value = true;

      // تحديث قائمة التشغيل إذا تم توفيرها
      if (playlist != null) {
        currentPlaylist.assignAll(playlist);
        currentIndex.value = playlist.indexWhere((media) => media.id == item.id);
        if (currentIndex.value == -1) currentIndex.value = 0;
      }

      // تحديث اسم قائمة التشغيل
      if (playlistName != null) {
        currentPlaylistName.value = playlistName;
      }

      // تحديث الملف الحالي
      currentMediaItem.value = item;

      // تشغيل الملف
      await _audioPlayer.setAudioSource(AudioSource.uri(Uri.file(item.path)));
      await _audioPlayer.play();

      // إظهار المشغل
      showPlayer();

    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تشغيل الملف: ${item.title}');
    } finally {
      isLoading.value = false;
    }
  }

  /// تشغيل/إيقاف التشغيل
  Future<void> togglePlayPause() async {
    if (isPlaying.value) {
      await _audioPlayer.pause();
    } else {
      await _audioPlayer.play();
    }
  }

  /// الانتقال للملف التالي
  Future<void> playNext() async {
    if (currentPlaylist.isEmpty) return;

    int nextIndex;
    if (isShuffleEnabled.value) {
      nextIndex = _getNextShuffledIndex();
    } else {
      nextIndex = (currentIndex.value + 1) % currentPlaylist.length;
    }

    currentIndex.value = nextIndex;
    await playMediaItem(currentPlaylist[nextIndex]);
  }

  /// الانتقال للملف السابق
  Future<void> playPrevious() async {
    if (currentPlaylist.isEmpty) return;

    int prevIndex;
    if (isShuffleEnabled.value) {
      prevIndex = _getPreviousShuffledIndex();
    } else {
      prevIndex = currentIndex.value - 1;
      if (prevIndex < 0) prevIndex = currentPlaylist.length - 1;
    }

    currentIndex.value = prevIndex;
    await playMediaItem(currentPlaylist[prevIndex]);
  }

  /// البحث في الملف
  Future<void> seekTo(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// تغيير سرعة التشغيل
  Future<void> setPlaybackSpeed(double speed) async {
    playbackSpeed.value = speed;
    await _audioPlayer.setSpeed(speed);
  }

  /// تبديل وضع التكرار
  void toggleRepeatMode() {
    switch (repeatMode.value) {
      case RepeatMode.none:
        repeatMode.value = RepeatMode.one;
        break;
      case RepeatMode.one:
        repeatMode.value = RepeatMode.all;
        break;
      case RepeatMode.all:
        repeatMode.value = RepeatMode.none;
        break;
    }
  }

  /// تبديل وضع الخلط
  void toggleShuffle() {
    isShuffleEnabled.value = !isShuffleEnabled.value;
    if (isShuffleEnabled.value) {
      _generateShuffledIndices();
    }
  }

  /// إظهار المشغل
  void showPlayer({bool fullScreen = false}) {
    isPlayerVisible.value = true;
    isFullScreen.value = fullScreen;
    isMinimized.value = !fullScreen;
  }

  /// إخفاء المشغل
  void hidePlayer() {
    isPlayerVisible.value = false;
    isMinimized.value = true;
    isFullScreen.value = false;
  }

  /// تبديل بين المشغل المصغر والكامل
  void togglePlayerSize() {
    if (isFullScreen.value) {
      // من الكامل إلى المصغر
      isFullScreen.value = false;
      isMinimized.value = true;
    } else {
      // من المصغر إلى الكامل
      isFullScreen.value = true;
      isMinimized.value = false;
    }
  }

  /// ترتيب قائمة التشغيل
  void sortPlaylist(SortOrder order, {bool ascending = true}) {
    sortOrder.value = order;
    isAscending.value = ascending;

    List<MediaItem> sortedList = List.from(currentPlaylist);
    
    switch (order) {
      case SortOrder.name:
        sortedList.sort((a, b) => ascending 
          ? a.title.compareTo(b.title) 
          : b.title.compareTo(a.title));
        break;
      case SortOrder.artist:
        sortedList.sort((a, b) => ascending 
          ? (a.artist ?? '').compareTo(b.artist ?? '') 
          : (b.artist ?? '').compareTo(a.artist ?? ''));
        break;
      case SortOrder.album:
        sortedList.sort((a, b) => ascending 
          ? (a.album ?? '').compareTo(b.album ?? '') 
          : (b.album ?? '').compareTo(a.album ?? ''));
        break;
      case SortOrder.dateAdded:
        sortedList.sort((a, b) => ascending 
          ? (a.dateAdded ?? DateTime.now()).compareTo(b.dateAdded ?? DateTime.now())
          : (b.dateAdded ?? DateTime.now()).compareTo(a.dateAdded ?? DateTime.now()));
        break;
      case SortOrder.duration:
        sortedList.sort((a, b) => ascending 
          ? (a.duration ?? Duration.zero).compareTo(b.duration ?? Duration.zero)
          : (b.duration ?? Duration.zero).compareTo(a.duration ?? Duration.zero));
        break;
    }

    currentPlaylist.assignAll(sortedList);
    // تحديث الفهرس الحالي
    if (currentMediaItem.value != null) {
      currentIndex.value = currentPlaylist.indexWhere(
        (item) => item.id == currentMediaItem.value!.id
      );
    }
  }

  /// معالجة انتهاء تشغيل الملف
  void _onTrackCompleted() {
    switch (repeatMode.value) {
      case RepeatMode.one:
        // إعادة تشغيل نفس الملف
        _audioPlayer.seek(Duration.zero);
        _audioPlayer.play();
        break;
      case RepeatMode.all:
        // الانتقال للملف التالي
        playNext();
        break;
      case RepeatMode.none:
        // التوقف أو الانتقال للتالي إذا لم يكن آخر ملف
        if (currentIndex.value < currentPlaylist.length - 1) {
          playNext();
        }
        break;
    }
  }

  /// توليد فهارس الخلط
  void _generateShuffledIndices() {
    shuffledIndices.clear();
    shuffledIndices.addAll(List.generate(currentPlaylist.length, (index) => index));
    shuffledIndices.shuffle();
  }

  /// الحصول على الفهرس التالي في وضع الخلط
  int _getNextShuffledIndex() {
    int currentShuffledIndex = shuffledIndices.indexOf(currentIndex.value);
    return shuffledIndices[(currentShuffledIndex + 1) % shuffledIndices.length];
  }

  /// الحصول على الفهرس السابق في وضع الخلط
  int _getPreviousShuffledIndex() {
    int currentShuffledIndex = shuffledIndices.indexOf(currentIndex.value);
    int prevIndex = currentShuffledIndex - 1;
    if (prevIndex < 0) prevIndex = shuffledIndices.length - 1;
    return shuffledIndices[prevIndex];
  }

  // Getters للمعلومات الحالية
  String get currentTitle => currentMediaItem.value?.title ?? '';
  String get currentArtist => currentMediaItem.value?.artist ?? 'مجهول';
  String get currentAlbum => currentMediaItem.value?.album ?? 'مجهول';
  bool get hasCurrentTrack => currentMediaItem.value != null;
  String get formattedPosition => _formatDuration(currentPosition.value);
  String get formattedDuration => _formatDuration(totalDuration.value);

  /// تنسيق المدة الزمنية
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

/// أنواع التكرار
enum RepeatMode { none, one, all }

/// أنواع الترتيب
enum SortOrder { name, artist, album, dateAdded, duration }
