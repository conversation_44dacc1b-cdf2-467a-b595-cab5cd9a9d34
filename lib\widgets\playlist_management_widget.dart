import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/unified_audio_player_service.dart';
import '../model/media_item.dart';

/// ويدجت إدارة قائمة التشغيل
/// يدعم ترتيب الملفات بالسحب وأزرار التكرار
class PlaylistManagementWidget extends StatefulWidget {
  const PlaylistManagementWidget({super.key});

  @override
  State<PlaylistManagementWidget> createState() => _PlaylistManagementWidgetState();
}

class _PlaylistManagementWidgetState extends State<PlaylistManagementWidget> {
  final UnifiedAudioPlayerService _playerService = UnifiedAudioPlayerService.instance;
  bool _isReorderMode = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // رأس القائمة
          _buildHeader(),
          
          // قائمة الملفات
          Expanded(
            child: _buildPlaylistView(),
          ),
          
          // أزرار التحكم السفلية
          _buildBottomControls(),
        ],
      ),
    );
  }

  /// بناء رأس القائمة
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // مؤشر السحب
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // عنوان القائمة وأزرار التحكم
          Row(
            children: [
              Icon(
                Icons.queue_music,
                color: Get.theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(() => Text(
                      _playerService.currentPlaylistName.value,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Get.theme.colorScheme.onSurface,
                      ),
                    )),
                    Obx(() => Text(
                      '${_playerService.currentPlaylist.length} أغنية',
                      style: TextStyle(
                        fontSize: 14,
                        color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    )),
                  ],
                ),
              ),
              
              // زر الترتيب
              IconButton(
                icon: Icon(
                  _isReorderMode ? Icons.check : Icons.sort,
                  color: Get.theme.colorScheme.primary,
                ),
                onPressed: () {
                  setState(() {
                    _isReorderMode = !_isReorderMode;
                  });
                },
                tooltip: _isReorderMode ? 'إنهاء الترتيب' : 'ترتيب القائمة',
              ),
              
              // زر الخيارات
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  color: Get.theme.colorScheme.onSurface,
                ),
                onSelected: _handlePlaylistAction,
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'sort_name',
                    child: Row(
                      children: [
                        Icon(Icons.sort_by_alpha),
                        SizedBox(width: 8),
                        Text('ترتيب حسب الاسم'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'sort_artist',
                    child: Row(
                      children: [
                        Icon(Icons.person),
                        SizedBox(width: 8),
                        Text('ترتيب حسب الفنان'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'sort_date',
                    child: Row(
                      children: [
                        Icon(Icons.date_range),
                        SizedBox(width: 8),
                        Text('ترتيب حسب التاريخ'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'shuffle_all',
                    child: Row(
                      children: [
                        Icon(Icons.shuffle),
                        SizedBox(width: 8),
                        Text('خلط القائمة'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear_all',
                    child: Row(
                      children: [
                        Icon(Icons.clear_all, color: Colors.red),
                        SizedBox(width: 8),
                        Text('مسح القائمة', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عرض قائمة التشغيل
  Widget _buildPlaylistView() {
    return Obx(() {
      final playlist = _playerService.currentPlaylist;
      
      if (playlist.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.queue_music, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('قائمة التشغيل فارغة'),
            ],
          ),
        );
      }

      if (_isReorderMode) {
        return _buildReorderableList(playlist);
      } else {
        return _buildNormalList(playlist);
      }
    });
  }

  /// بناء القائمة العادية
  Widget _buildNormalList(List<MediaItem> playlist) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: playlist.length,
      itemBuilder: (context, index) {
        final item = playlist[index];
        final isCurrentTrack = _playerService.currentIndex.value == index;
        
        return _buildTrackTile(item, index, isCurrentTrack);
      },
    );
  }

  /// بناء القائمة القابلة للترتيب
  Widget _buildReorderableList(List<MediaItem> playlist) {
    return ReorderableListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: playlist.length,
      onReorder: (oldIndex, newIndex) {
        if (newIndex > oldIndex) newIndex--;
        
        final item = playlist.removeAt(oldIndex);
        playlist.insert(newIndex, item);
        
        // تحديث الفهرس الحالي إذا لزم الأمر
        if (_playerService.currentIndex.value == oldIndex) {
          _playerService.currentIndex.value = newIndex;
        } else if (oldIndex < _playerService.currentIndex.value && 
                   newIndex >= _playerService.currentIndex.value) {
          _playerService.currentIndex.value--;
        } else if (oldIndex > _playerService.currentIndex.value && 
                   newIndex <= _playerService.currentIndex.value) {
          _playerService.currentIndex.value++;
        }
      },
      itemBuilder: (context, index) {
        final item = playlist[index];
        final isCurrentTrack = _playerService.currentIndex.value == index;
        
        return _buildReorderableTrackTile(item, index, isCurrentTrack);
      },
    );
  }

  /// بناء عنصر المسار العادي
  Widget _buildTrackTile(MediaItem item, int index, bool isCurrentTrack) {
    return Container(
      key: ValueKey(item.id),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isCurrentTrack 
            ? Get.theme.colorScheme.primary.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isCurrentTrack 
            ? Border.all(color: Get.theme.colorScheme.primary.withValues(alpha: 0.3))
            : null,
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isCurrentTrack 
                ? Get.theme.colorScheme.primary
                : Get.theme.colorScheme.primary.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Center(
            child: isCurrentTrack 
                ? Icon(
                    _playerService.isPlaying.value ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 20,
                  )
                : Text(
                    '${index + 1}',
                    style: TextStyle(
                      color: Get.theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
          ),
        ),
        title: Text(
          item.title,
          style: TextStyle(
            fontWeight: isCurrentTrack ? FontWeight.bold : FontWeight.normal,
            color: isCurrentTrack 
                ? Get.theme.colorScheme.primary
                : Get.theme.colorScheme.onSurface,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          item.artist ?? 'مجهول',
          style: TextStyle(
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            fontSize: 12,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          onSelected: (action) => _handleTrackAction(action, item, index),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'remove',
              child: Row(
                children: [
                  Icon(Icons.remove_circle_outline, color: Colors.red),
                  SizedBox(width: 8),
                  Text('إزالة من القائمة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'move_up',
              child: Row(
                children: [
                  Icon(Icons.keyboard_arrow_up),
                  SizedBox(width: 8),
                  Text('نقل للأعلى'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'move_down',
              child: Row(
                children: [
                  Icon(Icons.keyboard_arrow_down),
                  SizedBox(width: 8),
                  Text('نقل للأسفل'),
                ],
              ),
            ),
          ],
        ),
        onTap: () {
          _playerService.currentIndex.value = index;
          _playerService.playMediaItem(item);
        },
      ),
    );
  }

  /// بناء عنصر المسار القابل للترتيب
  Widget _buildReorderableTrackTile(MediaItem item, int index, bool isCurrentTrack) {
    return Container(
      key: ValueKey(item.id),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isCurrentTrack 
            ? Get.theme.colorScheme.primary.withValues(alpha: 0.1)
            : Get.theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCurrentTrack 
              ? Get.theme.colorScheme.primary.withValues(alpha: 0.3)
              : Get.theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isCurrentTrack 
                ? Get.theme.colorScheme.primary
                : Get.theme.colorScheme.primary.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Center(
            child: Text(
              '${index + 1}',
              style: TextStyle(
                color: isCurrentTrack ? Colors.white : Get.theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),
        title: Text(
          item.title,
          style: TextStyle(
            fontWeight: isCurrentTrack ? FontWeight.bold : FontWeight.normal,
            color: isCurrentTrack 
                ? Get.theme.colorScheme.primary
                : Get.theme.colorScheme.onSurface,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Text(
          item.artist ?? 'مجهول',
          style: TextStyle(
            color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            fontSize: 12,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: Icon(
          Icons.drag_handle,
          color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
    );
  }

  /// بناء أزرار التحكم السفلية
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Get.theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // تكرار الحالي
          Obx(() => _buildRepeatButton(
            RepeatMode.one,
            Icons.repeat_one,
            'تكرار الحالي',
            _playerService.repeatMode.value == RepeatMode.one,
          )),
          
          // تكرار الكل
          Obx(() => _buildRepeatButton(
            RepeatMode.all,
            Icons.repeat,
            'تكرار الكل',
            _playerService.repeatMode.value == RepeatMode.all,
          )),
          
          // بدون تكرار
          Obx(() => _buildRepeatButton(
            RepeatMode.none,
            Icons.repeat_outlined,
            'بدون تكرار',
            _playerService.repeatMode.value == RepeatMode.none,
          )),
        ],
      ),
    );
  }

  /// بناء زر التكرار
  Widget _buildRepeatButton(RepeatMode mode, IconData icon, String label, bool isActive) {
    return GestureDetector(
      onTap: () => _playerService.repeatMode.value = mode,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isActive 
              ? Get.theme.colorScheme.primary
              : Get.theme.colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive 
                  ? Colors.white
                  : Get.theme.colorScheme.primary,
              size: 18,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isActive 
                    ? Colors.white
                    : Get.theme.colorScheme.primary,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// معالجة إجراءات قائمة التشغيل
  void _handlePlaylistAction(String action) {
    switch (action) {
      case 'sort_name':
        _playerService.sortPlaylist(SortOrder.name);
        break;
      case 'sort_artist':
        _playerService.sortPlaylist(SortOrder.artist);
        break;
      case 'sort_date':
        _playerService.sortPlaylist(SortOrder.dateAdded);
        break;
      case 'shuffle_all':
        _playerService.toggleShuffle();
        break;
      case 'clear_all':
        _showClearPlaylistConfirmation();
        break;
    }
  }

  /// معالجة إجراءات المسار
  void _handleTrackAction(String action, MediaItem item, int index) {
    switch (action) {
      case 'remove':
        _playerService.currentPlaylist.removeAt(index);
        if (_playerService.currentIndex.value > index) {
          _playerService.currentIndex.value--;
        }
        break;
      case 'move_up':
        if (index > 0) {
          final playlist = _playerService.currentPlaylist;
          final item = playlist.removeAt(index);
          playlist.insert(index - 1, item);
          if (_playerService.currentIndex.value == index) {
            _playerService.currentIndex.value = index - 1;
          }
        }
        break;
      case 'move_down':
        if (index < _playerService.currentPlaylist.length - 1) {
          final playlist = _playerService.currentPlaylist;
          final item = playlist.removeAt(index);
          playlist.insert(index + 1, item);
          if (_playerService.currentIndex.value == index) {
            _playerService.currentIndex.value = index + 1;
          }
        }
        break;
    }
  }

  /// عرض تأكيد مسح القائمة
  void _showClearPlaylistConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('مسح قائمة التشغيل'),
        content: const Text('هل تريد مسح جميع الملفات من قائمة التشغيل الحالية؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _playerService.currentPlaylist.clear();
              _playerService.hidePlayer();
              Get.back();
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
