import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/unified_audio_player_service.dart';

/// المشغل الكامل المحسن
/// يدعم السحب للتصغير والتنقل بين الملفات بالسحب
class EnhancedFullPlayer extends StatefulWidget {
  const EnhancedFullPlayer({super.key});

  @override
  State<EnhancedFullPlayer> createState() => _EnhancedFullPlayerState();
}

class _EnhancedFullPlayerState extends State<EnhancedFullPlayer>
    with TickerProviderStateMixin {
  late AnimationController _albumArtController;
  late Animation<double> _albumArtAnimation;
  
  final UnifiedAudioPlayerService _playerService = UnifiedAudioPlayerService.instance;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _albumArtController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    
    _albumArtAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_albumArtController);
    
    // بدء دوران صورة الألبوم إذا كان يتم التشغيل
    _playerService.isPlaying.listen((playing) {
      if (playing) {
        _albumArtController.repeat();
      } else {
        _albumArtController.stop();
      }
    });
  }

  @override
  void dispose() {
    _albumArtController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!_playerService.isPlayerVisible.value || 
          !_playerService.isFullScreen.value ||
          !_playerService.hasCurrentTrack) {
        return const SizedBox.shrink();
      }

      return Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Get.theme.colorScheme.primary.withValues(alpha: 0.8),
                Get.theme.colorScheme.secondary.withValues(alpha: 0.6),
                Get.theme.scaffoldBackgroundColor,
              ],
            ),
          ),
          child: SafeArea(
            child: GestureDetector(
              onVerticalDragEnd: (details) {
                // السحب للأسفل للتصغير
                if (details.primaryVelocity! > 300) {
                  _playerService.togglePlayerSize();
                }
              },
              child: Column(
                children: [
                  // شريط التنقل العلوي
                  _buildAppBar(),
                  
                  // المحتوى الرئيسي
                  Expanded(
                    child: PageView.builder(
                      controller: _pageController,
                      itemCount: _playerService.currentPlaylist.length,
                      onPageChanged: (index) {
                        if (index != _playerService.currentIndex.value) {
                          _playerService.currentIndex.value = index;
                          _playerService.playMediaItem(_playerService.currentPlaylist[index]);
                        }
                      },
                      itemBuilder: (context, index) {
                        return _buildPlayerContent();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  /// بناء شريط التنقل العلوي
  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              Icons.keyboard_arrow_down,
              color: Get.theme.colorScheme.onPrimary,
              size: 28,
            ),
            onPressed: () => _playerService.togglePlayerSize(),
          ),
          
          Expanded(
            child: Column(
              children: [
                Obx(() => Text(
                  _playerService.currentPlaylistName.value,
                  style: TextStyle(
                    color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                )),
                const SizedBox(height: 2),
                Text(
                  'تشغيل من قائمة التشغيل',
                  style: TextStyle(
                    color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.6),
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color: Get.theme.colorScheme.onPrimary,
            ),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف الملف'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'hide',
                child: Row(
                  children: [
                    Icon(Icons.visibility_off),
                    SizedBox(width: 8),
                    Text('إخفاء الملف'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('تعديل معلومات الملف'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'audio_settings',
                child: Row(
                  children: [
                    Icon(Icons.equalizer),
                    SizedBox(width: 8),
                    Text('إعدادات الصوت'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء محتوى المشغل
  Widget _buildPlayerContent() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // صورة الألبوم
          Expanded(
            flex: 3,
            child: _buildAlbumArt(),
          ),
          
          const SizedBox(height: 32),
          
          // معلومات الأغنية
          _buildSongInfo(),
          
          const SizedBox(height: 24),
          
          // شريط التقدم
          _buildProgressBar(),
          
          const SizedBox(height: 32),
          
          // أزرار التحكم
          _buildControlButtons(),
          
          const SizedBox(height: 24),
          
          // أزرار إضافية
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// بناء صورة الألبوم
  Widget _buildAlbumArt() {
    return GestureDetector(
      onHorizontalDragEnd: (details) {
        // السحب الأفقي للتنقل بين الملفات
        if (details.primaryVelocity! > 300) {
          // سحب لليمين - الملف السابق
          _playerService.playPrevious();
        } else if (details.primaryVelocity! < -300) {
          // سحب لليسار - الملف التالي
          _playerService.playNext();
        }
      },
      child: Center(
        child: RotationTransition(
          turns: _albumArtAnimation,
          child: Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  Get.theme.colorScheme.primary,
                  Get.theme.colorScheme.secondary,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 30,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              Icons.music_note,
              size: 120,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء معلومات الأغنية
  Widget _buildSongInfo() {
    return Column(
      children: [
        Obx(() => Text(
          _playerService.currentTitle,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Get.theme.colorScheme.onPrimary,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        )),
        
        const SizedBox(height: 8),
        
        Obx(() => Text(
          _playerService.currentArtist,
          style: TextStyle(
            fontSize: 16,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          textAlign: TextAlign.center,
        )),
      ],
    );
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar() {
    return Column(
      children: [
        Obx(() => SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Get.theme.colorScheme.onPrimary,
            inactiveTrackColor: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.3),
            thumbColor: Get.theme.colorScheme.onPrimary,
            overlayColor: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.2),
          ),
          child: Slider(
            value: _playerService.currentPosition.value.inSeconds.toDouble(),
            max: _playerService.totalDuration.value.inSeconds.toDouble(),
            onChanged: (value) {
              _playerService.seekTo(Duration(seconds: value.toInt()));
            },
          ),
        )),
        
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Obx(() => Text(
                _playerService.formattedPosition,
                style: TextStyle(
                  color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              )),
              Obx(() => Text(
                _playerService.formattedDuration,
                style: TextStyle(
                  color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              )),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Obx(() => IconButton(
          icon: Icon(
            _playerService.isShuffleEnabled.value 
                ? Icons.shuffle_on_outlined 
                : Icons.shuffle,
            color: _playerService.isShuffleEnabled.value 
                ? Get.theme.colorScheme.onPrimary
                : Get.theme.colorScheme.onPrimary.withValues(alpha: 0.6),
          ),
          onPressed: _playerService.toggleShuffle,
        )),
        
        IconButton(
          icon: Icon(
            Icons.skip_previous,
            color: Get.theme.colorScheme.onPrimary,
            size: 36,
          ),
          onPressed: _playerService.playPrevious,
        ),
        
        Obx(() => Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Get.theme.colorScheme.onPrimary,
          ),
          child: IconButton(
            icon: Icon(
              _playerService.isPlaying.value ? Icons.pause : Icons.play_arrow,
              color: Get.theme.colorScheme.primary,
              size: 36,
            ),
            onPressed: _playerService.togglePlayPause,
          ),
        )),
        
        IconButton(
          icon: Icon(
            Icons.skip_next,
            color: Get.theme.colorScheme.onPrimary,
            size: 36,
          ),
          onPressed: _playerService.playNext,
        ),
        
        Obx(() => IconButton(
          icon: Icon(
            _getRepeatIcon(),
            color: _playerService.repeatMode.value != RepeatMode.none 
                ? Get.theme.colorScheme.onPrimary
                : Get.theme.colorScheme.onPrimary.withValues(alpha: 0.6),
          ),
          onPressed: _playerService.toggleRepeatMode,
        )),
      ],
    );
  }

  /// بناء الأزرار الإضافية
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        IconButton(
          icon: Icon(
            Icons.favorite_border,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // إضافة للمفضلة
          },
        ),
        
        IconButton(
          icon: Icon(
            Icons.playlist_add,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // إضافة لقائمة تشغيل
          },
        ),
        
        IconButton(
          icon: Icon(
            Icons.queue_music,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // عرض قائمة التشغيل الحالية
            _showCurrentPlaylist();
          },
        ),
        
        IconButton(
          icon: Icon(
            Icons.share,
            color: Get.theme.colorScheme.onPrimary.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // مشاركة الملف
          },
        ),
      ],
    );
  }

  /// الحصول على أيقونة التكرار
  IconData _getRepeatIcon() {
    switch (_playerService.repeatMode.value) {
      case RepeatMode.none:
        return Icons.repeat;
      case RepeatMode.one:
        return Icons.repeat_one;
      case RepeatMode.all:
        return Icons.repeat_on;
    }
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'delete':
        _showDeleteConfirmation();
        break;
      case 'hide':
        _showHideConfirmation();
        break;
      case 'edit':
        _showEditDialog();
        break;
      case 'audio_settings':
        _showAudioSettings();
        break;
    }
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف الملف'),
        content: Text('هل تريد حذف "${_playerService.currentTitle}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // تنفيذ الحذف
              Get.back();
              Get.snackbar('تم', 'تم حذف الملف بنجاح');
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد الإخفاء
  void _showHideConfirmation() {
    Get.dialog(
      AlertDialog(
        title: const Text('إخفاء الملف'),
        content: Text('هل تريد إخفاء "${_playerService.currentTitle}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // تنفيذ الإخفاء
              Get.back();
              Get.snackbar('تم', 'تم إخفاء الملف بنجاح');
            },
            child: const Text('إخفاء'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار التعديل
  void _showEditDialog() {
    // TODO: تنفيذ حوار تعديل معلومات الملف
    Get.snackbar('قريباً', 'ميزة تعديل معلومات الملف ستكون متاحة قريباً');
  }

  /// عرض إعدادات الصوت
  void _showAudioSettings() {
    // TODO: تنفيذ إعدادات الصوت
    Get.snackbar('قريباً', 'إعدادات الصوت ستكون متاحة قريباً');
  }

  /// عرض قائمة التشغيل الحالية
  void _showCurrentPlaylist() {
    _playerService.togglePlayerSize();
  }
}
