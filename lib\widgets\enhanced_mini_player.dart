import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/unified_audio_player_service.dart';
import '../model/media_item.dart';
import 'media_card.dart';

/// المشغل المصغر المحسن
/// يظهر في أسفل الشاشة مع قائمة الملفات وإمكانية السحب للتكبير
class EnhancedMiniPlayer extends StatefulWidget {
  const EnhancedMiniPlayer({super.key});

  @override
  State<EnhancedMiniPlayer> createState() => _EnhancedMiniPlayerState();
}

class _EnhancedMiniPlayerState extends State<EnhancedMiniPlayer>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  final DraggableScrollableController _scrollController = DraggableScrollableController();
  final UnifiedAudioPlayerService _playerService = UnifiedAudioPlayerService.instance;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!_playerService.isPlayerVisible.value || 
          !_playerService.isMinimized.value ||
          !_playerService.hasCurrentTrack) {
        return const SizedBox.shrink();
      }

      return DraggableScrollableSheet(
        controller: _scrollController,
        initialChildSize: 0.12, // حجم المشغل المصغر
        minChildSize: 0.12,
        maxChildSize: 0.9, // الحد الأقصى للتوسع
        snap: true,
        snapSizes: const [0.12, 0.5, 0.9],
        builder: (context, scrollController) {
          return Container(
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.surface,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              children: [
                // مؤشر السحب
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                
                // المشغل المصغر
                _buildMiniPlayerHeader(),
                
                // قائمة الملفات (تظهر عند السحب)
                Expanded(
                  child: _buildPlaylistView(scrollController),
                ),
              ],
            ),
          );
        },
      );
    });
  }

  /// بناء رأس المشغل المصغر
  Widget _buildMiniPlayerHeader() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // صورة الألبوم
          GestureDetector(
            onTap: () => _playerService.togglePlayerSize(),
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.primary.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Icon(
                Icons.music_note,
                color: Get.theme.colorScheme.primary,
                size: 24,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // معلومات الأغنية
          Expanded(
            child: GestureDetector(
              onTap: () => _playerService.togglePlayerSize(),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(() => Text(
                    _playerService.currentTitle,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Get.theme.colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )),
                  const SizedBox(height: 2),
                  Obx(() => Text(
                    _playerService.currentArtist,
                    style: TextStyle(
                      color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )),
                ],
              ),
            ),
          ),
          
          // أزرار التحكم
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.skip_previous),
                color: Get.theme.colorScheme.onSurface,
                onPressed: _playerService.playPrevious,
              ),
              Obx(() => IconButton(
                icon: Icon(
                  _playerService.isPlaying.value
                      ? Icons.pause
                      : Icons.play_arrow,
                ),
                color: Get.theme.colorScheme.primary,
                onPressed: _playerService.togglePlayPause,
              )),
              IconButton(
                icon: const Icon(Icons.skip_next),
                color: Get.theme.colorScheme.onSurface,
                onPressed: _playerService.playNext,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عرض قائمة التشغيل
  Widget _buildPlaylistView(ScrollController scrollController) {
    return Obx(() {
      final playlist = _playerService.currentPlaylist;
      
      if (playlist.isEmpty) {
        return const Center(
          child: Text('لا توجد ملفات في قائمة التشغيل'),
        );
      }

      return Column(
        children: [
          // عنوان قائمة التشغيل
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.queue_music,
                  color: Get.theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _playerService.currentPlaylistName.value,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                Text(
                  '${playlist.length} أغنية',
                  style: TextStyle(
                    color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          // قائمة الملفات
          Expanded(
            child: ListView.builder(
              controller: scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: playlist.length,
              itemBuilder: (context, index) {
                final item = playlist[index];
                final isCurrentTrack = _playerService.currentIndex.value == index;
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: isCurrentTrack 
                        ? Get.theme.colorScheme.primary.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: isCurrentTrack 
                        ? Border.all(
                            color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
                          )
                        : null,
                  ),
                  child: ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: isCurrentTrack 
                            ? Get.theme.colorScheme.primary
                            : Get.theme.colorScheme.primary.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        isCurrentTrack ? Icons.equalizer : Icons.music_note,
                        color: isCurrentTrack 
                            ? Colors.white
                            : Get.theme.colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      item.title,
                      style: TextStyle(
                        fontWeight: isCurrentTrack ? FontWeight.bold : FontWeight.normal,
                        color: isCurrentTrack 
                            ? Get.theme.colorScheme.primary
                            : Get.theme.colorScheme.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    subtitle: Text(
                      item.artist ?? 'مجهول',
                      style: TextStyle(
                        color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: isCurrentTrack 
                        ? Icon(
                            _playerService.isPlaying.value 
                                ? Icons.pause_circle_filled
                                : Icons.play_circle_filled,
                            color: Get.theme.colorScheme.primary,
                          )
                        : null,
                    onTap: () {
                      _playerService.currentIndex.value = index;
                      _playerService.playMediaItem(item);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }
}
